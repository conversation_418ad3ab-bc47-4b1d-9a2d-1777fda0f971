import dayjs from 'dayjs'
import utils from '@/utils/utils'
import { rebateTypeList, rebateCalculationFrequencyList } from './index'

export default {
  props: {
    detailInfo: {
      type: Object,
      default: () => {}
    },
    list: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      loading: false,
      tableData: []
    }
  },
  watch: {
    list: {
      handler(val) {
        let tableData = val
        this.type === 'detail' && (tableData = this.serializeList(val))
        this.tableData = tableData
      },
      deep: true,
      immediate: true
    }
  },
  computed: {
    editable() {
      return [1, 3, 6].includes(this.detailInfo.status)
    },
    tableRef() {
      return this.$refs.scTableRef.$refs.xGrid
    },
    toolbar() {
      switch (this.type) {
        case 'purcahseAttachment':
          return [
            {
              code: 'upload',
              name: this.$t('上传'),
              status: 'info'
              // isHidden: !this.editable
            },
            {
              code: 'delete',
              name: this.$t('删除'),
              status: 'info'
              // isHidden: !this.editable
            },
            {
              code: 'download',
              name: this.$t('下载'),
              status: 'info'
            }
          ]
        case 'supplierAttachment':
          return [
            {
              code: 'download',
              name: this.$t('下载'),
              status: 'info'
            }
          ]
        default:
          return []
      }
    },
    cellTools() {
      switch (this.type) {
        case 'purcahseAttachment':
          return ['download', 'delete']
        case 'supplierAttachment':
          return ['download']
        default:
          return []
      }
    },
    columns() {
      switch (this.type) {
        case 'detail':
          return [
            {
              width: 50,
              type: 'checkbox'
            },
            {
              field: 'lineNumber',
              title: this.$t('行号'),
              width: 50
            },
            {
              field: 'rounds',
              title: this.$t('返利轮次')
            },
            {
              field: 'startDate',
              title: this.$t('返利时间从'),
              slots: {
                default: ({ row }) => {
                  return [
                    <span>
                      {row.startDate ? utils.formateTime(Number(row.startDate), 'yyyy-MM-dd') : ''}
                    </span>
                  ]
                }
              }
            },
            {
              field: 'endDate',
              title: this.$t('返利时间至'),
              slots: {
                default: ({ row }) => {
                  return [
                    <span>
                      {row.endDate ? utils.formateTime(Number(row.endDate), 'yyyy-MM-dd') : ''}
                    </span>
                  ]
                }
              }
            },
            {
              field: 'factoryName',
              title: this.$t('工厂'),
              minWidth: 160,
              slots: {
                default: ({ row }) => {
                  return [
                    <span>{row.factoryCode ? row.factoryCode + '-' + row.factoryName : ''}</span>
                  ]
                }
              }
            },
            {
              field: 'categoryCode',
              title: this.$t('物料类别'),
              minWidth: 140
            },
            {
              field: 'categoryName',
              title: this.$t('物料类别名称'),
              minWidth: 140
            },
            {
              field: 'materialCode',
              title: this.$t('物料编码'),
              minWidth: 140
            },
            {
              field: 'materialName',
              title: this.$t('物料名称'),
              minWidth: 140
            },
            {
              field: 'rebateType',
              title: this.$t('返利类型'),
              minWidth: 280,
              slots: {
                default: ({ row }) => {
                  const selectedItem = rebateTypeList.find((item) => item.value === row.rebateType)
                  const rebateTypeName = selectedItem?.text || null
                  return [<span>{rebateTypeName}</span>]
                }
              }
            },
            {
              field: 'rebateFreq',
              title: this.$t('返利计算频次'),
              slots: {
                default: ({ row }) => {
                  const selectedItem = rebateCalculationFrequencyList.find(
                    (item) => item.value === row.rebateFreq
                  )
                  const rebateFreqName = selectedItem?.text || null
                  return [<span>{rebateFreqName}</span>]
                }
              }
            },
            {
              field: 'actualStartDate',
              title: this.$t('实际返利起始时间'),
              minWidth: 160,
              slots: {
                default: ({ row }) => {
                  const actualStartDate = Number(row.actualStartDate)
                    ? dayjs(Number(row.actualStartDate)).format('YYYY-MM-DD')
                    : row.actualStartDate
                  return [<span>{actualStartDate}</span>]
                }
              }
            },
            {
              field: 'taxRate',
              title: this.$t('税率')
            },
            {
              field: 'currency',
              title: this.$t('币种')
            },
            {
              field: 'untaxedAmt',
              title: this.$t('返利金额（未税）'),
              minWidth: 160,
              slots: {
                default: ({ row }) => {
                  const untaxedAmt = this.formatAmount(row.untaxedAmt)
                  return [<span>{untaxedAmt}</span>]
                }
              }
            },
            {
              field: 'rebateRatio',
              title: this.$t('返利比例（%）'),
              minWidth: 140
            },
            {
              field: 'priceDiff',
              title: this.$t('单片差价')
            },
            {
              field: 'ladderInfo',
              title: this.$t('阶梯等级'),
              slots: {
                default: ({ row, column }) => {
                  // const isStep = [2, 3, 6, 7].includes(row.rebateType)
                  return [
                    <div>
                      <a
                        // v-show={isStep}
                        style='color: #409eff'
                        on-click={() => this.handleClickCellTitle(row, column)}>
                        {this.$t('阶梯等级')}
                      </a>
                      {/* <span v-show={!isStep}>-</span> */}
                    </div>
                  ]
                }
              }
            },
            {
              field: 'payCashRatio',
              title: this.$t('付现比例')
            },
            {
              field: 'settleQty',
              title: this.$t('当前结算数量'),
              minWidth: 160
            },
            {
              field: 'settleAmt',
              title: this.$t('当前结算金额'),
              minWidth: 160,
              slots: {
                default: ({ row }) => {
                  const settleAmt = this.formatAmount(row.settleAmt)
                  return [<span>{settleAmt}</span>]
                }
              }
            },
            {
              field: 'amtUntaxed',
              title: this.$t('返利金额（不含税）'),
              minWidth: 180,
              slots: {
                default: ({ row }) => {
                  const amtUntaxed = this.formatAmount(row.amtUntaxed)
                  return [<span>{amtUntaxed}</span>]
                }
              }
            },
            {
              field: 'amtTaxed',
              title: this.$t('返利金额（含税）'),
              minWidth: 180,
              slots: {
                default: ({ row }) => {
                  const amtTaxed = this.formatAmount(row.amtTaxed)
                  return [<span>{amtTaxed}</span>]
                }
              }
            },
            {
              field: 'profitCenter',
              title: this.$t('利润中心')
            },
            {
              field: 'sourceCreatedDate',
              title: this.$t('协议创建时间'),
              slots: {
                default: ({ row }) => {
                  const sourceCreatedDate = Number(row.sourceCreatedDate)
                    ? dayjs(Number(row.sourceCreatedDate)).format('YYYY-MM-DD HH:mm:ss')
                    : row.sourceCreatedDate
                  return [<span>{sourceCreatedDate}</span>]
                }
              }
            },
            {
              field: 'createTime',
              title: this.$t('金额创建时间'),
              slots: {
                default: ({ row }) => {
                  const createTime = Number(row.createTime)
                    ? dayjs(Number(row.createTime)).format('YYYY-MM-DD HH:mm:ss')
                    : row.createTime
                  return [<span>{createTime}</span>]
                }
              }
            },
            {
              field: 'statusDesc',
              title: this.$t('金额行状态')
            },
            {
              field: 'remark',
              title: this.$t('备注'),
              minWidth: 140
            }
          ]
        case 'purcahseAttachment':
        case 'supplierAttachment':
          return [
            {
              width: 50,
              type: 'checkbox'
            },
            // {
            //   title: this.$t('序号'),
            //   width: 50,
            //   type: 'seq'
            // },
            {
              field: 'attachmentName',
              title: this.$t('附件名称'),
              slots: {
                default: ({ row, column }) => {
                  return [
                    <div>
                      <a
                        style='display: block; color: #409eff'
                        on-click={() => this.handleClickCellTitle(row, column)}>
                        {row.attachmentName}
                      </a>
                      <a
                        class='cell-btn'
                        v-show={this.cellTools.includes('download')}
                        on-click={() => this.handleClickCellTool('download', row)}>
                        <i class='vxe-icon-download' />
                        {this.$t('下载')}
                      </a>
                    </div>
                  ]
                }
              }
            },
            {
              field: 'attachmentSize',
              title: this.$t('附件大小'),
              slots: {
                default: ({ row }) => {
                  return [
                    <span>{Number(((row.attachmentSize / 1024) * 100) / 100).toFixed(2)}KB</span>
                  ]
                }
              }
            },
            {
              field: 'uploadUserName',
              title: this.$t('上传人')
            },
            {
              field: 'uploadTime',
              title: this.$t('上传时间'),
              slots: {
                default: ({ row }) => {
                  const uploadTime = row.uploadTime
                    ? dayjs(Number(row.uploadTime)).format('YYYY-MM-DD HH:mm:ss')
                    : null
                  return [<span>{uploadTime}</span>]
                }
              }
            }
          ]
        default:
          return []
      }
    }
  },
  mounted() {},
  methods: {
    // 限制日期选择
    disabledDateMethod(e) {
      const { date } = e
      const day = dayjs(date).get('date')
      const currentBu = localStorage.getItem('currentBu')

      // 空调事业部只能选择当前日期往后的每月23号，其他事业部选择每月5号
      const allowedDay = currentBu === 'KT' ? 23 : 5

      return Date.now() - 86400000 >= new Date(date).getTime() || day !== allowedDay
    },
    // 序列化列表
    serializeList(list) {
      list?.forEach((item) => {
        if (item.actualStartDate && !isNaN(Number(item.actualStartDate))) {
          item.stepList = item.ladderInfo ? JSON.parse(item.ladderInfo) : []
          item.actualStartDate = dayjs(Number(item.actualStartDate)).format('YYYY-MM-DD')
        }
      })
      return list
    },
    // 金额千分位显示
    formatAmount(amount, precision = 2) {
      if (!amount && amount !== 0) {
        return ''
      }
      const newAmount = amount?.toFixed(+precision)
      const decimalPlace = precision ? newAmount?.split('.')[1] : ''
      let res = amount?.toLocaleString()
      if (precision || precision === 0) {
        const intNum = res?.split('.')[0]
        res = precision === 0 ? intNum : intNum + '.' + decimalPlace
      }
      return res
    }
  }
}
